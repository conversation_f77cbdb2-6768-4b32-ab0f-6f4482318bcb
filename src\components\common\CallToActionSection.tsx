import React, { useEffect, useRef } from 'react';
import { cn } from '../../utils';

interface CallToActionSectionProps {
  className?: string;
}

export const CallToActionSection: React.FC<CallToActionSectionProps> = ({ className }) => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate');
          }
        });
      },
      { threshold: 0.1 }
    );

    const animatedElements = sectionRef.current?.querySelectorAll('.animate-on-scroll, .animate-on-scroll-scale');
    animatedElements?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      className={cn('section-padding relative overflow-hidden', className)}
    >
      {/* Background with <PERSON><PERSON>radient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-600 via-primary-700 to-accent-purple-700">
        <div className="absolute inset-0 bg-mesh opacity-20"></div>
      </div>

      {/* Floating Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-40 h-40 bg-white/10 rounded-full blur-3xl floating-element" />
        <div className="absolute top-40 right-20 w-32 h-32 bg-accent-emerald-500/20 rounded-full blur-2xl floating-element" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-1/4 w-48 h-48 bg-accent-cyan-500/15 rounded-full blur-3xl floating-element" style={{ animationDelay: '4s' }} />
        <div className="absolute bottom-40 right-1/3 w-24 h-24 bg-accent-amber-500/20 rounded-full blur-xl floating-element" style={{ animationDelay: '6s' }} />
      </div>

      {/* Pattern Overlay */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpolygon points='50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40'/%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container-custom relative z-10">
        <div className="max-w-5xl mx-auto text-center text-white">
          {/* Main Content */}
          <div className="animate-on-scroll">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/20 backdrop-blur-sm border border-white/30 mb-8">
              <span className="w-2 h-2 bg-accent-emerald-400 rounded-full mr-2 animate-pulse"></span>
              🚀 Start Your Journey Today
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold font-heading mb-8 leading-tight">
              Ready to Transform Your{' '}
              <span className="relative">
                English Skills?
                <svg className="absolute -bottom-2 left-0 w-full h-4" viewBox="0 0 300 12" fill="none">
                  <path d="M3 9C50 3 100 1 150 3C200 5 250 7 297 9" stroke="rgba(255,255,255,0.6)" strokeWidth="3" strokeLinecap="round" fill="none"/>
                </svg>
              </span>
            </h2>

            <p className="text-xl lg:text-2xl mb-12 opacity-90 max-w-4xl mx-auto leading-relaxed">
              Join over 10,000 students who have already improved their English proficiency
              with our proven learning methodology. Start your transformation today!
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="animate-on-scroll">
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <button className="group relative bg-white text-primary-600 hover:bg-secondary-50 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                <span className="flex items-center justify-center">
                  Start Free Trial
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-accent-emerald-400/20 to-accent-cyan-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>

              <button className="group relative bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary-600 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105">
                <span className="flex items-center justify-center">
                  <svg className="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  View Pricing Plans
                </span>
              </button>
            </div>
          </div>

          {/* Student Community Section */}
          <div className="animate-on-scroll">
            <div className="pt-12 border-t border-white/20">
              {/* Student Avatars Grid */}
              <div className="relative max-w-4xl mx-auto mb-12">
                <h3 className="text-2xl md:text-3xl font-bold text-center mb-8">
                  Join Over <span className="text-accent-emerald-400">5 Million</span> Students
                </h3>

                {/* Floating Student Avatars */}
                <div className="relative h-64 overflow-hidden">
                  {/* Top Row */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-16 h-16 rounded-full overflow-hidden border-4 border-white/30 floating-element">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Second Row */}
                  <div className="absolute top-12 left-1/4 w-14 h-14 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '1s' }}>
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-12 right-1/4 w-14 h-14 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '2s' }}>
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Third Row */}
                  <div className="absolute top-24 left-16 w-12 h-12 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '3s' }}>
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-24 right-16 w-12 h-12 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '4s' }}>
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Fourth Row */}
                  <div className="absolute top-36 left-8 w-10 h-10 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '5s' }}>
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-36 right-8 w-10 h-10 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '6s' }}>
                    <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Fifth Row */}
                  <div className="absolute top-48 left-1/3 w-12 h-12 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '7s' }}>
                    <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-48 right-1/3 w-12 h-12 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '8s' }}>
                    <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Additional scattered avatars */}
                  <div className="absolute top-20 left-2 w-8 h-8 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '9s' }}>
                    <img src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-20 right-2 w-8 h-8 rounded-full overflow-hidden border-4 border-white/30 floating-element" style={{ animationDelay: '10s' }}>
                    <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Colorful dots for decoration */}
                  <div className="absolute top-8 left-12 w-4 h-4 bg-accent-rose-400 rounded-full floating-element" style={{ animationDelay: '11s' }}></div>
                  <div className="absolute top-8 right-12 w-4 h-4 bg-accent-emerald-400 rounded-full floating-element" style={{ animationDelay: '12s' }}></div>
                  <div className="absolute bottom-8 left-20 w-6 h-6 bg-accent-amber-400 rounded-full floating-element" style={{ animationDelay: '13s' }}></div>
                  <div className="absolute bottom-8 right-20 w-6 h-6 bg-accent-cyan-400 rounded-full floating-element" style={{ animationDelay: '14s' }}></div>
                  <div className="absolute top-32 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-accent-purple-400 rounded-full floating-element" style={{ animationDelay: '15s' }}></div>
                </div>

                <p className="text-center text-lg opacity-90 max-w-2xl mx-auto">
                  Nullam at elementum odio auctor dui. Donec non nunc sodales massa
                  finibus tempor tortor magna feu nibh.
                </p>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <div className="text-center group">
                  <div className="text-5xl md:text-6xl font-bold mb-2 bg-gradient-to-r from-accent-rose-400 to-accent-rose-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    1068
                  </div>
                  <div className="text-lg opacity-80">Online Courses</div>
                </div>

                <div className="text-center group">
                  <div className="text-5xl md:text-6xl font-bold mb-2 bg-gradient-to-r from-accent-cyan-400 to-accent-cyan-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    225
                  </div>
                  <div className="text-lg opacity-80">Total Instructors</div>
                </div>

                <div className="text-center group">
                  <div className="text-5xl md:text-6xl font-bold mb-2 bg-gradient-to-r from-accent-emerald-400 to-accent-emerald-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    500K
                  </div>
                  <div className="text-lg opacity-80">Students Worldwide</div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Trust Elements */}
          <div className="animate-on-scroll">
            <div className="flex flex-wrap items-center justify-center gap-8 mt-12 pt-8 border-t border-white/20 text-sm opacity-80">
              <div className="flex items-center">
                <svg className="w-5 h-5 text-accent-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                SSL Secured
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-accent-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Money-back Guarantee
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-accent-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                24/7 Support
              </div>
              <div className="flex items-center">
                <svg className="w-5 h-5 text-accent-emerald-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                Instant Access
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
